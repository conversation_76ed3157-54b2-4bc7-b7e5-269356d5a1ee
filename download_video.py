from sharepoint_simple import get_access_token, get_site_id, download_large_file_with_progress

def main():
    print("🎥 Video Download Tool")
    print("=" * 50)
    
    # Authenticate
    print("🔐 Authenticating...")
    access_token = get_access_token()
    
    if not access_token:
        print("❌ Authentication failed")
        return
    
    print("✔️ Authentication successful!")
    
    # Get site ID
    site_id = get_site_id(access_token)
    if not site_id:
        print("❌ Cannot connect to SharePoint")
        return
    
    print("✔️ SharePoint connection successful!")
    
    # Download the video file
    video_filename = "test video.mp4"
    print(f"\n🎬 Downloading video: {video_filename}")
    
    success = download_large_file_with_progress(access_token, site_id, video_filename)
    
    if success:
        print(f"\n🎉 Video download completed successfully!")
        print(f"📁 File saved as: {video_filename}")
        
        #
        import os
        if os.path.exists(video_filename):
            file_size = os.path.getsize(video_filename)
            file_size_mb = file_size / (1024 * 1024)
            print(f"📊 Local file size: {file_size_mb:.2f} MB")
    else:
        print(f"\n❌ Video download failed!")

if __name__ == "__main__":
    main()
