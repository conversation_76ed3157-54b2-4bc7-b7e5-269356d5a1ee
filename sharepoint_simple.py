import requests
import pandas as pd
import os
from msal import ConfidentialClientApplication

# SharePoint Configuration
CONFIG = {
    "client_id": "29be3219-b709-4950-b119-53958725bc80",
    "client_secret": "****************************************",
    "tenant_id": "9cb2bebd-6862-4d5f-8a77-ee591e704d99",
    "site_hostname": "taureauai.sharepoint.com",
    "site_path": "/sites/genai",
    "folder_path": "Test"
}

def get_access_token():
    """Get access token from Microsoft Graph API"""
    app = ConfidentialClientApplication(
        CONFIG["client_id"],
        authority=f"https://login.microsoftonline.com/{CONFIG['tenant_id']}",
        client_credential=CONFIG["client_secret"]
    )
    token_response = app.acquire_token_for_client(scopes=["https://graph.microsoft.com/.default"])

    if not token_response:
        print("❌ Failed to acquire token")
        return None
    
    return token_response.get("access_token")

def get_site_id(access_token):
    """Get Site ID from SharePoint"""
    url = f"https://graph.microsoft.com/v1.0/sites/{CONFIG['site_hostname']}:{CONFIG['site_path']}"
    response = requests.get(url, headers={"Authorization": f"Bearer {access_token}"})
    return response.json()["id"] if response.status_code == 200 else None

def list_and_analyze_files(access_token, site_id):
    """List all files in SharePoint folder and analyze them"""
    # Get list of files in the folder
    url = f"https://graph.microsoft.com/v1.0/sites/{site_id}/drive/root:/{CONFIG['folder_path']}:/children"
    response = requests.get(url, headers={"Authorization": f"Bearer {access_token}"})

    if response.status_code != 200:
        print(f"❌ Cannot access folder: {response.status_code}")
        return

    files = response.json().get("value", [])

    if not files:
        print("📭 No files found in the folder")
        return

    print(f"📁 Found {len(files)} file(s) in folder '{CONFIG['folder_path']}':")
    print("-" * 80)

    for i, file in enumerate(files, 1):
        file_type = "📁" if file.get("folder") else "📄"
        file_size = file.get("size", 0)
        file_size_mb = file_size / (1024 * 1024)  # Convert to MB
        created_time = file.get("createdDateTime", "Unknown")
        modified_time = file.get("lastModifiedDateTime", "Unknown")

        print(f"{i}. {file_type} {file['name']}")
        print(f"   📏 Size: {file_size_mb:.2f} MB ({file_size:,} bytes)")
        print(f"   📅 Created: {created_time}")
        print(f"   📝 Modified: {modified_time}")

        # Check if it's a video file
        if file['name'].lower().endswith(('.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv')):
            print(f"   🎬 Video file detected!")

        print()

    return files

def download_specific_file(access_token, site_id, filename):
    """Download a specific file from SharePoint"""
    # Get list of files first
    url = f"https://graph.microsoft.com/v1.0/sites/{site_id}/drive/root:/{CONFIG['folder_path']}:/children"
    response = requests.get(url, headers={"Authorization": f"Bearer {access_token}"})

    if response.status_code != 200:
        print(f"❌ Cannot access folder: {response.status_code}")
        return False

    files = response.json().get("value", [])
    target_file = next((f for f in files if f['name'] == filename), None)

    if not target_file:
        print(f"❌ File {filename} not found")
        return False

    file_size = target_file.get("size", 0)
    file_size_mb = file_size / (1024 * 1024)

    print(f"📥 Downloading {filename} ({file_size_mb:.2f} MB)...")

    # Download file
    download_url = f"https://graph.microsoft.com/v1.0/sites/{site_id}/drive/items/{target_file['id']}/content"
    download_response = requests.get(download_url, headers={"Authorization": f"Bearer {access_token}"})

    if download_response.status_code == 200:
        with open(filename, 'wb') as f:
            f.write(download_response.content)

        print(f"✔️ File {filename} downloaded successfully!")
        print(f"📊 File size: {file_size_mb:.2f} MB")

        # Basic file analysis
        if filename.lower().endswith(('.xlsx', '.xls')):
            try:
                df = pd.read_excel(filename)
                print(f"� Excel analysis: {len(df)} rows × {len(df.columns)} columns")
            except Exception as e:
                print(f"⚠️ Could not analyze Excel file: {e}")
        elif filename.lower().endswith(('.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv')):
            print(f"🎬 Video file downloaded - ready for processing!")

        return True
    else:
        print(f"❌ Error downloading file: {download_response.status_code}")
        return False

def download_large_file_with_progress(access_token, site_id, filename):
    """Download large file with progress tracking"""
    # Get list of files first
    url = f"https://graph.microsoft.com/v1.0/sites/{site_id}/drive/root:/{CONFIG['folder_path']}:/children"
    response = requests.get(url, headers={"Authorization": f"Bearer {access_token}"})

    if response.status_code != 200:
        print(f"❌ Cannot access folder: {response.status_code}")
        return False

    files = response.json().get("value", [])
    target_file = next((f for f in files if f['name'] == filename), None)

    if not target_file:
        print(f"❌ File {filename} not found")
        return False

    file_size = target_file.get("size", 0)
    file_size_mb = file_size / (1024 * 1024)

    print(f"📥 Starting download: {filename}")
    print(f"📏 File size: {file_size_mb:.2f} MB ({file_size:,} bytes)")

    # Check if file already exists
    if os.path.exists(filename):
        local_size = os.path.getsize(filename)
        if local_size == file_size:
            print(f"✔️ File already exists and is complete!")
            return True
        else:
            print(f"⚠️ Local file exists but size mismatch. Re-downloading...")

    # Download with streaming
    download_url = f"https://graph.microsoft.com/v1.0/sites/{site_id}/drive/items/{target_file['id']}/content"

    try:
        with requests.get(download_url, headers={"Authorization": f"Bearer {access_token}"}, stream=True) as r:
            r.raise_for_status()

            downloaded = 0
            chunk_size = 8192  # 8KB chunks

            with open(filename, 'wb') as f:
                for chunk in r.iter_content(chunk_size=chunk_size):
                    if chunk:
                        f.write(chunk)
                        downloaded += len(chunk)

                        # Simple progress indicator
                        progress = (downloaded / file_size) * 100
                        mb_downloaded = downloaded / (1024 * 1024)

                        # Print progress every 10MB or at completion
                        if downloaded % (10 * 1024 * 1024) < chunk_size or downloaded == file_size:
                            print(f"📊 Progress: {progress:.1f}% ({mb_downloaded:.1f}/{file_size_mb:.1f} MB)")

        print(f"✔️ Download completed: {filename}")

        # Verify file size
        actual_size = os.path.getsize(filename)
        if actual_size == file_size:
            print(f"✔️ File integrity verified!")
            return True
        else:
            print(f"❌ File size mismatch! Expected: {file_size}, Got: {actual_size}")
            return False

    except Exception as e:
        print(f"❌ Download failed: {e}")
        return False

# Run main program
if __name__ == "__main__":
    print("🔐 Authenticating...")
    access_token = get_access_token()
    
    if access_token:
        print("✔️ Authentication successful!")
        
        site_id = get_site_id(access_token)
        if site_id:
            print("✅ SharePoint connection successful!")

            # List all files first
            files = list_and_analyze_files(access_token, site_id)

            # Look for video files specifically
            if files:
                video_files = [f for f in files if f['name'].lower().endswith(('.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv'))]
                if video_files:
                    print(f"\n🎬 Found {len(video_files)} video file(s)!")
                    for video in video_files:
                        print(f"   📹 {video['name']} ({video.get('size', 0) / (1024*1024):.2f} MB)")
        else:
            print("❌ Cannot connect to SharePoint")
    else:
        print("❌ Authentication failed")
