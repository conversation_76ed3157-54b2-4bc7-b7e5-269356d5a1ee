📁 SharePoint API Download bằng Python

🎯 Mục tiêu
Sử dụng Microsoft Graph API để tải về các file Excel và Video từ SharePoint bằng Python ( pandas ).

📋 Các bước thực hiện

1. Kết nối SharePoint qua app configuration
   • <PERSON><PERSON><PERSON> cập vào site SharePoint (ví dụ: https://taureauai.sharepoint.com/sites/genai )
   • Sử dụng Microsoft Graph API với app credentials
   • Authentication thông qua MSAL library

2. Sử dụng các API download document xử lý dữ liệu file lớn. Đánh giá tốc độ reading và writing xem sao
   • Check folder structure thông qua API calls
   • Video file: test video.mp4 (large file testing)

🐍 Python Script để download file từ SharePoint API

import requests
import pandas as pd
from msal import ConfidentialClientApplication

# SharePoint config
CONFIG = {
    "client_id": "29be3219-b709-4950-b119-53958725bc80",
    "site_hostname": "taureauai.sharepoint.com",
    "site_path": "/sites/genai",
    "folder_path": "Test"
}

try:
    # Authentication và download
    access_token = get_access_token()
    site_id = get_site_id(access_token)
    files = list_and_analyze_files(access_token, site_id)
    
    # Download video file
    success = download_large_file_with_progress(access_token, site_id, "test video.mp4")
    print("✅ Download completed!")
    
except Exception as e:
    print(f"❌ Error: {e}")

📊 Kết quả thử nghiệm

| Local File | Số dòng | Kết quả |
|------------|---------|---------|
| Download với progress tracking | ✅ | Dùng skiprows và nrows trong pandas |

❌ Tổng kết

• Phương pháp API hoạt động tốt, không có vấn đề token hay quyền truy cập.
• Tốc độ download nhanh, phù hợp để xử lý file lớn với progress tracking.
• Có thể sử dụng với các file Excel khác: thoa đơn, báo cáo, dữ liệu training 🚀
